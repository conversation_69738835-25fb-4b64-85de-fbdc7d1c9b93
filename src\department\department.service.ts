import { BadRequestException, Injectable } from '@nestjs/common';
import { AuthorizationQueue } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { CreateDepartmentDto } from './dto/create-department.dto';
import { DeleteDepartmentDto } from './dto/delete-department.dto';
import { UpdateDepartmentDto } from './dto/update-department.dto';

@Injectable()
export class DepartmentService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  async findDepartment({
    identifier,
    token,
  }: {
    identifier: string;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const department = this.databaseService.department.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            name: `${decodedToken.companyId}|${identifier}`,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });

    return department;
  }
  // Method to find a role by ID or Name
  async getDepartments(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    console.log(decodedToken);

    try {
      const { companyId } = decodedToken;
      const departments = await this.databaseService.department.findMany({
        where: {
          companyId,
        },
        include: {
          branch: {
            select: {
              name: true,
            },
          },
        },
      });

      return departments.map((department) => ({
        ...department,
        branch: department.branch ? department.branch.name.split('|')[1] : '-',
        name: department.name.split('|')[1],
      }));
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptDepartmentAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action as ACTIONS_CONSTANT) {
      case ACTIONS_CONSTANT.create: {
        const { name, description } = JSON.parse(
          queue.data,
        ) as CreateDepartmentDto;

        // Check if the group already exists by name
        const departmentExist = await this.databaseService.department.findFirst(
          {
            where: {
              name: {
                equals: `${companyId}|${name}`,
                mode: 'insensitive',
              },
            },
          },
        );

        if (departmentExist) {
          throw new BadRequestException('Department already exists');
        }

        await this.databaseService.department.create({
          data: {
            name: `${companyId}|${name}`,
            description: description || name,
            companyId,
            createdBy: requestedBy,
            approvedBy,
          },
        });

        return true;
      }

      case ACTIONS_CONSTANT.update: {
        const payload = JSON.parse(queue.data) as UpdateDepartmentDto;
        const departmentExist = await this.databaseService.department.findFirst(
          {
            where: {
              name: {
                equals: `${companyId}|${payload.name}`,
                mode: 'insensitive',
              },
            },
          },
        );

        if (departmentExist) {
          throw new BadRequestException('Department already exists');
        }

        await this.updateDepartment({
          companyId,
          payload,
        });

        return true;
      }
      case ACTIONS_CONSTANT.delete: {
        const payload = JSON.parse(queue.data) as DeleteDepartmentDto;

        await this.deleteDepartment({
          companyId,
          payload,
        });

        return true;
      }
      default:
        return false;
    }
  }

  async createDepartment({
    payload,
    token,
  }: {
    payload: CreateDepartmentDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      action: ACTIONS_CONSTANT.create,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.DEPARTMENT,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async updateDepartment({
    payload,
    companyId,
  }: {
    payload: UpdateDepartmentDto;
    companyId: string;
  }) {
    const { description, name, id } = payload;

    try {
      if (!id) {
        throw new BadRequestException('Department Id is required.');
      }

      const departmentExist = await this.databaseService.department.findUnique({
        where: {
          id,
          companyId,
        },
      });

      if (!departmentExist) {
        throw new BadRequestException('Department not found.');
      }

      await this.databaseService.department.update({
        where: {
          id: departmentExist.id,
        },
        data: {
          name: name ? `${companyId}|${name}` : departmentExist.name,
          description: description ? description : name,
        },
      });

      return;
    } catch (error) {
      console.log('Error updating department:', error);

      throw error;
    }
  }

  async deleteDepartment({
    payload,
    companyId,
  }: {
    payload: DeleteDepartmentDto;
    companyId: string;
  }) {
    const { id } = payload;

    try {
      if (!id) {
        throw new BadRequestException('Department Id is required.');
      }

      const departmentExist = await this.databaseService.department.findUnique({
        where: {
          id,
          companyId,
        },
      });

      if (!departmentExist) {
        throw new BadRequestException('Department not found.');
      }

      await this.databaseService.department.update({
        where: {
          id: departmentExist.id,
        },
        data: {
          status: 'INACTIVE',
        },
      });

      return;
    } catch (error) {
      console.log('Error deleting department', error);

      throw error;
    }
  }

  async deleteDepartmentRequest({
    payload,
    token,
  }: {
    payload: DeleteDepartmentDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.delete,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.DEPARTMENT,
        requestedBy: decodedToken.name,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateDepartmentequest({
    payload,
    token,
  }: {
    payload: UpdateDepartmentDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.update,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.DEPARTMENT,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }
}
