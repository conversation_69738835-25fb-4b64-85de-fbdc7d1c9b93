import { BadRequestException, Injectable } from '@nestjs/common';
import { AuthorizationQueue } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { BranchService } from 'src/branch/branch.service';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { CreateSubBranchDto } from './dto/create-sub-branch.dto';
import { DeleteSubBranchDto } from './dto/delete-sub-branch.dto';
import { UpdateSubBranchDto } from './dto/update-sub-branch.dto';

@Injectable()
export class SubBranchService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,

    private readonly branchService: BranchService,
  ) {}

  async findSubBranchByNameAndBranch({
    name,
    branchId,
  }: {
    branchId: string;
    name: string;
  }) {
    const branch = await this.databaseService.subBranch.findFirst({
      where: {
        name: {
          equals: name,
          mode: 'insensitive',
        },
        branchId,
      },
    });

    return branch;
  }

  // Method to find a role by ID or Name
  async getSubBranches(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    console.log(decodedToken);

    try {
      const { companyId } = decodedToken;
      const subBranches = await this.databaseService.subBranch.findMany({
        where: {
          companyId,
        },
        include: {
          branch: {
            select: {
              name: true,
            },
          },
        },
      });

      return subBranches.map((sb) => ({
        ...sb,
        name: sb.name.split('|')[1],
        branch: sb.branch?.name.split('|')[1],
      }));
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptSubBranchAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action as ACTIONS_CONSTANT) {
      case ACTIONS_CONSTANT.create: {
        const { name, description, branch } = JSON.parse(
          queue.data,
        ) as CreateSubBranchDto;

        if (!branch || !name || !companyId) {
          throw new BadRequestException('Missing required field');
        }

        const branchExist = await this.branchService.findBranchByName(
          `${companyId}|${branch}`,
        );

        if (!branchExist) {
          throw new BadRequestException("Branch doesn't exist");
        }

        const subBranchExist = await this.findSubBranchByNameAndBranch({
          name: `${companyId}|${name}`,
          branchId: branchExist.id,
        });

        if (subBranchExist) {
          throw new BadRequestException('Sub branch exist');
        }

        await this.databaseService.subBranch.create({
          data: {
            name: `${companyId}|${name}`,
            description: description || name,
            branchId: branchExist.id,
            companyId,
            createdBy: requestedBy,
            approvedBy,
          },
        });

        return true;
      }

      case ACTIONS_CONSTANT.update: {
        const payload = JSON.parse(queue.data) as UpdateSubBranchDto;

        await this.updateSubBranch({
          companyId,
          payload,
        });

        return true;
      }
      case ACTIONS_CONSTANT.delete: {
        const payload = JSON.parse(queue.data) as DeleteSubBranchDto;

        await this.deleteSubBranch({
          companyId,
          payload,
        });

        return true;
      }

      default:
        return false;
    }
  }

  async createSubBranch({
    payload,
    token,
  }: {
    payload: CreateSubBranchDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const { branch, name, description } = payload;

    if (!branch || !name || !decodedToken.companyId) {
      throw new BadRequestException('Missing required field');
    }

    const branchExist = await this.branchService.findBranchByName(
      `${decodedToken.companyId}|${branch}`,
    );

    if (!branchExist) {
      throw new BadRequestException("Branch doesn't exist");
    }

    const subBranchExist = await this.findSubBranchByNameAndBranch({
      name: `${decodedToken.companyId}|${name}`,
      branchId: branchExist.id,
    });

    if (subBranchExist) {
      throw new BadRequestException('Sub branch exist');
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload: { name, description, branch },
      companyId: decodedToken.companyId,
      action: ACTIONS_CONSTANT.create,
      module: MODULE_CONSTANT.SUB_BRANCH,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async updateSubBranchRequest({
    payload,
    token,
  }: {
    payload: UpdateSubBranchDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.update,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.SUB_BRANCH,
        requestedBy: decodedToken.name,
      });
      return;
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateSubBranch({
    payload,
    companyId,
  }: {
    payload: UpdateSubBranchDto;
    companyId: string;
  }) {
    const { description, name, id } = payload;

    try {
      if (!id) {
        throw new BadRequestException('Unit Id is required.');
      }

      const unitExist = await this.databaseService.unit.findUnique({
        where: {
          id,
          companyId,
        },
      });

      if (!unitExist) {
        throw new BadRequestException('Unit not found.');
      }

      await this.databaseService.unit.update({
        where: {
          id: unitExist.id,
        },
        data: {
          name: name ? `${companyId}|${name}` : unitExist.name,
          companyId,
          description: description ? description : name,
        },
      });

      return;
    } catch (error) {
      console.log('Error updating unit:', error);

      throw error;
    }
  }

  async deleteSubBranchRequest({
    payload,
    token,
  }: {
    payload: DeleteSubBranchDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.delete,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.SUB_BRANCH,
        requestedBy: decodedToken.name,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async deleteSubBranch({
    payload,
    companyId,
  }: {
    payload: DeleteSubBranchDto;
    companyId: string;
  }) {
    const { id } = payload;

    try {
      if (!id) {
        throw new BadRequestException('Unit is required.');
      }

      const unitExist = await this.databaseService.unit.findUnique({
        where: {
          id,
          companyId,
        },
      });

      if (!unitExist) {
        throw new BadRequestException('Unit not found.');
      }

      await this.databaseService.unit.update({
        where: {
          id: unitExist.id,
        },
        data: {
          status: 'INACTIVE',
        },
      });

      return;
    } catch (error) {
      console.log('Error deleting unit:', error);

      throw error;
    }
  }
}
