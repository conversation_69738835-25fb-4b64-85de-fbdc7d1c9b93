import { BadRequestException, Injectable } from '@nestjs/common';
import { AuthorizationQueue } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';

import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { CreateRegionDto } from './dto/create-region.dto';
import { DeleteRegionDto } from './dto/delete-region.dto';
import { UpdateRegionDto } from './dto/update-region.dto';

@Injectable()
export class RegionService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  async findRegionByName(name: string) {
    const region = await this.databaseService.region.findUnique({
      where: {
        name,
      },
    });

    return region;
  }

  async findRegion({
    identifier,
    token,
  }: {
    identifier: string;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const region = await this.databaseService.region.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            name: `${decodedToken.companyId}|${identifier}`,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });

    return region;
  }
  // Method to find a role by ID or Name
  async getRegions(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    console.log(decodedToken);

    try {
      const { companyId } = decodedToken;
      const regions = await this.databaseService.region.findMany({
        where: {
          companyId,
        },
      });

      return regions.map((region) => ({
        ...region,
        name: region.name.split('|')[1],
      }));
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptRegionAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action as ACTIONS_CONSTANT) {
      case ACTIONS_CONSTANT.create: {
        const { name, description } = JSON.parse(queue.data) as CreateRegionDto;

        // Check if the group already exists by name
        const regionExist = await this.databaseService.region.findFirst({
          where: {
            companyId,
            name: {
              equals: name,
              mode: 'insensitive',
            },
          },
        });

        if (regionExist) {
          throw new BadRequestException('Region already exists');
        }

        await this.databaseService.region.create({
          data: {
            name: `${companyId}|${name}`,
            description: description || name,
            companyId,
            createdBy: requestedBy,
            approvedBy,
          },
        });

        return true;
      }

      case ACTIONS_CONSTANT.update: {
        const payload = JSON.parse(queue.data) as UpdateRegionDto;

        await this.updateRegion({
          companyId,
          payload,
        });

        return true;
      }
      case ACTIONS_CONSTANT.delete: {
        const payload = JSON.parse(queue.data) as DeleteRegionDto;

        await this.deleteRegion({
          companyId,
          payload,
        });

        return true;
      }

      default:
        return false;
    }
  }

  async createRegion({
    payload,
    token,
  }: {
    payload: CreateRegionDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      companyId: decodedToken.companyId,
      action: ACTIONS_CONSTANT.create,
      module: MODULE_CONSTANT.REGION,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async updateRegionRequest({
    payload,
    token,
  }: {
    payload: UpdateRegionDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.update,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.REGION,
        requestedBy: decodedToken.name,
      });
      return;
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateRegion({
    payload,
    companyId,
  }: {
    payload: UpdateRegionDto;
    companyId: string;
  }) {
    const { description, name, id } = payload;

    try {
      if (!id) {
        throw new BadRequestException('Region Id is required.');
      }

      const regionExist = await this.databaseService.region.findUnique({
        where: {
          id,
          companyId,
        },
      });

      if (!regionExist) {
        throw new BadRequestException('Region not found.');
      }

      await this.databaseService.region.update({
        where: {
          id: regionExist.id,
        },
        data: {
          name: name ? `${companyId}|${name}` : regionExist.name,
          companyId,
          description: description ? description : name,
        },
      });

      return;
    } catch (error) {
      console.log('Error updating region:', error);

      throw error;
    }
  }

  async deleteRegionRequest({
    payload,
    token,
  }: {
    payload: DeleteRegionDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.delete,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.REGION,
        requestedBy: decodedToken.name,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async deleteRegion({
    payload,
    companyId,
  }: {
    payload: DeleteRegionDto;
    companyId: string;
  }) {
    const { id } = payload;

    try {
      if (!id) {
        throw new BadRequestException('Region is required.');
      }

      const regionExist = await this.databaseService.region.findUnique({
        where: {
          id,
          companyId,
        },
      });

      if (!regionExist) {
        throw new BadRequestException('Region not found.');
      }

      await this.databaseService.region.update({
        where: {
          id: regionExist.id,
        },
        data: {
          status: 'INACTIVE',
        },
      });

      return;
    } catch (error) {
      console.log('Error deleting region:', error);

      throw error;
    }
  }
}
