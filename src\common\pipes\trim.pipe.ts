import { PipeTransform, Injectable, ArgumentMetadata } from '@nestjs/common';

@Injectable()
export class TrimPipe implements PipeTransform {
  private readonly excludeFields: string[] = [
    'password',
    'confirmPassword',
    'oldPassword',
    'newPassword',
    'currentPassword',
  ];

  transform(value: unknown, metadata: ArgumentMetadata): unknown {
    if (!value || typeof value !== 'object') {
      return value;
    }

    return this.trimObjectValues(value);
  }

  private trimObjectValues(obj: unknown): unknown {
    if (Array.isArray(obj)) {
      return (obj as unknown[]).map((item) => this.trimObjectValues(item));
    }

    if (obj && typeof obj === 'object') {
      const trimmedObj = {};

      for (const [key, value] of Object.entries(obj)) {
        if (typeof value === 'string' && !this.isPasswordField(key)) {
          trimmedObj[key] = value.trim();
        } else if (typeof value === 'object') {
          trimmedObj[key] = this.trimObjectValues(value);
        } else {
          trimmedObj[key] = value;
        }
      }

      return trimmedObj;
    }

    return obj;
  }

  private isPasswordField(fieldName: string): boolean {
    return this.excludeFields.some((excludeField) =>
      fieldName.toLowerCase().includes(excludeField.toLowerCase()),
    );
  }
}
